# =============================================================================
# Docker ignore file for Webscout
# Excludes unnecessary files from Docker build context
# =============================================================================

# -----------------------------------------------------------------------------
# Version Control
# -----------------------------------------------------------------------------
.git
.gitignore
.gitattributes
.gitmodules

# -----------------------------------------------------------------------------
# Documentation
# -----------------------------------------------------------------------------
*.md
docs/
README*
CHANGELOG*
LICENSE*
CONTRIBUTING*

# -----------------------------------------------------------------------------
# Python
# -----------------------------------------------------------------------------
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# -----------------------------------------------------------------------------
# IDEs and Editors
# -----------------------------------------------------------------------------
.vscode/
.vs/
*.swp
*.swo
*~

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.vim
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# -----------------------------------------------------------------------------
# Operating System
# -----------------------------------------------------------------------------
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# -----------------------------------------------------------------------------
# Docker
# -----------------------------------------------------------------------------
Dockerfile*
docker-compose*.yml
.dockerignore

# -----------------------------------------------------------------------------
# CI/CD
# -----------------------------------------------------------------------------
.github/
.gitlab-ci.yml
.travis.yml
.circleci/
azure-pipelines.yml
Jenkinsfile

# -----------------------------------------------------------------------------
# Logs and temporary files
# -----------------------------------------------------------------------------
*.log
logs/
tmp/
temp/
.tmp/

# -----------------------------------------------------------------------------
# Application specific
# -----------------------------------------------------------------------------
# Configuration files that shouldn't be in container
config.local.*
.env.local
.env.production
secrets/

# Data directories
data/
uploads/
downloads/

# Cache directories
.cache/
cache/

# Node.js (if any frontend components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# -----------------------------------------------------------------------------
# Security
# -----------------------------------------------------------------------------
*.key
*.pem
*.crt
*.p12
*.pfx
secrets.txt
.secrets/

# -----------------------------------------------------------------------------
# Backup files
# -----------------------------------------------------------------------------
*.bak
*.backup
*.old
*.orig

# -----------------------------------------------------------------------------
# Compressed files
# -----------------------------------------------------------------------------
*.zip
*.tar.gz
*.rar
*.7z

# -----------------------------------------------------------------------------
# Large files that shouldn't be in container
# -----------------------------------------------------------------------------
*.iso
*.dmg
*.img
